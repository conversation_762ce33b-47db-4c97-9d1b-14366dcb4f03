// Quick verification script - paste this in browser console
console.log("🔍 CSSDOST Theme Verification");
console.log("============================");

// Check localStorage
const storedTheme = localStorage.getItem("cssdost-theme");
console.log(`📦 Stored theme: ${storedTheme || "none (will use default)"}`);

// Check HTML classes
const hasLight = document.documentElement.classList.contains("light");
const hasDark = document.documentElement.classList.contains("dark");
console.log(`🎨 HTML classes: light=${hasLight}, dark=${hasDark}`);

// Check system preference
const systemPrefersDark = window.matchMedia("(prefers-color-scheme: dark)").matches;
console.log(`🖥️ System preference: ${systemPrefersDark ? "dark" : "light"}`);

// Determine effective theme
let effectiveTheme = "light"; // default
if (storedTheme === "dark") {
  effectiveTheme = "dark";
} else if (storedTheme === "system") {
  effectiveTheme = systemPrefersDark ? "dark" : "light";
}

console.log(`✨ Effective theme: ${effectiveTheme}`);
console.log(`✅ Light mode is ${effectiveTheme === "light" ? "ACTIVE" : "NOT active"}`);

// Quick reset to light mode if needed
if (effectiveTheme !== "light") {
  console.log("🔧 Resetting to light mode...");
  localStorage.setItem("cssdost-theme", "light");
  document.documentElement.classList.remove("dark");
  document.documentElement.classList.add("light");
  console.log("✅ Reset complete! Refresh page to see changes.");
}
