/**
 * Scholarly Serenity Theme Configuration
 * CSSDOST - Civil Service Exam Preparation App
 */

export const scholarlyTheme = {
  name: "Scholarly Serenity",
  description: "Professional and calming theme for CSS exam preparation",
  
  // Light Mode Colors
  light: {
    primary: {
      main: "#1E3A8A", // Navy Blue
      light: "#3B82F6",
      dark: "#1E40AF",
      contrast: "#FFFFFF"
    },
    secondary: {
      main: "#14B8A6", // Teal
      light: "#2DD4BF",
      dark: "#0D9488",
      contrast: "#FFFFFF"
    },
    background: {
      main: "#F8FAFC", // Soft White
      paper: "#FFFFFF",
      card: "#FFFFFF"
    },
    text: {
      primary: "#1F2937", // Charcoal Gray
      secondary: "#6B7280", // Slate Gray
      disabled: "#9CA3AF"
    },
    success: {
      main: "#84CC16", // Lime Green
      light: "#A3E635",
      dark: "#65A30D",
      contrast: "#1F2937"
    },
    warning: {
      main: "#F59E0B", // Amber
      light: "#FBBF24",
      dark: "#D97706",
      contrast: "#1F2937"
    },
    error: {
      main: "#F87171", // Soft Red
      light: "#FCA5A5",
      dark: "#EF4444",
      contrast: "#FFFFFF"
    },
    border: {
      main: "#E2E8F0",
      light: "#F1F5F9",
      dark: "#CBD5E1"
    }
  },
  
  // Dark Mode Colors
  dark: {
    primary: {
      main: "#1E40AF", // Lighter Navy Blue
      light: "#3B82F6",
      dark: "#1E3A8A",
      contrast: "#FFFFFF"
    },
    secondary: {
      main: "#2DD4BF", // Brighter Teal
      light: "#5EEAD4",
      dark: "#14B8A6",
      contrast: "#111827"
    },
    background: {
      main: "#111827", // Dark Slate
      paper: "#1F2937",
      card: "#1F2937"
    },
    text: {
      primary: "#E5E7EB", // Soft White
      secondary: "#9CA3AF", // Cool Gray
      disabled: "#6B7280"
    },
    success: {
      main: "#A3E635", // Brighter Lime Green
      light: "#BEF264",
      dark: "#84CC16",
      contrast: "#111827"
    },
    warning: {
      main: "#FBBF24", // Brighter Amber
      light: "#FCD34D",
      dark: "#F59E0B",
      contrast: "#111827"
    },
    error: {
      main: "#FCA5A5", // Soft Red
      light: "#FECACA",
      dark: "#F87171",
      contrast: "#FFFFFF"
    },
    border: {
      main: "#374151",
      light: "#4B5563",
      dark: "#1F2937"
    }
  }
};

// Utility functions for theme usage
export const getThemeColor = (mode: 'light' | 'dark', colorType: string, variant: string = 'main') => {
  const theme = scholarlyTheme[mode];
  const colorGroup = theme[colorType as keyof typeof theme];
  
  if (typeof colorGroup === 'object' && colorGroup !== null) {
    return (colorGroup as any)[variant] || (colorGroup as any).main;
  }
  
  return colorGroup;
};

// CSS Custom Properties for easy use in components
export const themeCSSVariables = {
  light: {
    '--primary': scholarlyTheme.light.primary.main,
    '--primary-light': scholarlyTheme.light.primary.light,
    '--primary-dark': scholarlyTheme.light.primary.dark,
    '--secondary': scholarlyTheme.light.secondary.main,
    '--secondary-light': scholarlyTheme.light.secondary.light,
    '--secondary-dark': scholarlyTheme.light.secondary.dark,
    '--background': scholarlyTheme.light.background.main,
    '--background-paper': scholarlyTheme.light.background.paper,
    '--text-primary': scholarlyTheme.light.text.primary,
    '--text-secondary': scholarlyTheme.light.text.secondary,
    '--success': scholarlyTheme.light.success.main,
    '--warning': scholarlyTheme.light.warning.main,
    '--error': scholarlyTheme.light.error.main,
    '--border': scholarlyTheme.light.border.main,
  },
  dark: {
    '--primary': scholarlyTheme.dark.primary.main,
    '--primary-light': scholarlyTheme.dark.primary.light,
    '--primary-dark': scholarlyTheme.dark.primary.dark,
    '--secondary': scholarlyTheme.dark.secondary.main,
    '--secondary-light': scholarlyTheme.dark.secondary.light,
    '--secondary-dark': scholarlyTheme.dark.secondary.dark,
    '--background': scholarlyTheme.dark.background.main,
    '--background-paper': scholarlyTheme.dark.background.paper,
    '--text-primary': scholarlyTheme.dark.text.primary,
    '--text-secondary': scholarlyTheme.dark.text.secondary,
    '--success': scholarlyTheme.dark.success.main,
    '--warning': scholarlyTheme.dark.warning.main,
    '--error': scholarlyTheme.dark.error.main,
    '--border': scholarlyTheme.dark.border.main,
  }
};

// Gradient utilities
export const scholarlyGradients = {
  primary: "linear-gradient(135deg, #1E3A8A 0%, #14B8A6 100%)",
  primaryDark: "linear-gradient(135deg, #1E40AF 0%, #2DD4BF 100%)",
  success: "linear-gradient(135deg, #84CC16 0%, #A3E635 100%)",
  warning: "linear-gradient(135deg, #F59E0B 0%, #FBBF24 100%)",
  error: "linear-gradient(135deg, #F87171 0%, #FCA5A5 100%)",
};

// Animation presets
export const scholarlyAnimations = {
  fadeIn: "fadeIn 0.3s ease-in-out",
  slideUp: "slideUp 0.3s ease-out",
  pulse: "pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite",
  bounce: "bounce 1s infinite",
};

// Typography scale
export const scholarlyTypography = {
  fontFamily: {
    primary: "var(--font-montserrat), system-ui, sans-serif",
    secondary: "Inter, system-ui, sans-serif",
    mono: "JetBrains Mono, monospace",
  },
  fontSize: {
    xs: "0.75rem",
    sm: "0.875rem",
    base: "1rem",
    lg: "1.125rem",
    xl: "1.25rem",
    "2xl": "1.5rem",
    "3xl": "1.875rem",
    "4xl": "2.25rem",
  },
  fontWeight: {
    light: "300",
    normal: "400",
    medium: "500",
    semibold: "600",
    bold: "700",
  },
};

// Spacing scale
export const scholarlySpacing = {
  xs: "0.25rem",
  sm: "0.5rem",
  md: "1rem",
  lg: "1.5rem",
  xl: "2rem",
  "2xl": "3rem",
  "3xl": "4rem",
};

// Border radius scale
export const scholarlyBorderRadius = {
  none: "0",
  sm: "0.125rem",
  base: "0.25rem",
  md: "0.375rem",
  lg: "0.5rem",
  xl: "0.75rem",
  "2xl": "1rem",
  full: "9999px",
};

// Shadow scale
export const scholarlyShadows = {
  sm: "0 1px 2px 0 rgba(0, 0, 0, 0.05)",
  base: "0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)",
  md: "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",
  lg: "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",
  xl: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
  "2xl": "0 25px 50px -12px rgba(0, 0, 0, 0.25)",
  inner: "inset 0 2px 4px 0 rgba(0, 0, 0, 0.06)",
  none: "none",
};

// Export all theme utilities
export default {
  scholarlyTheme,
  getThemeColor,
  themeCSSVariables,
  scholarlyGradients,
  scholarlyAnimations,
  scholarlyTypography,
  scholarlySpacing,
  scholarlyBorderRadius,
  scholarlyShadows,
};
