/**
 * Theme utility functions for CSSDOST
 */

/**
 * Clear the stored theme preference and reset to default
 */
export function clearStoredTheme(storageKey: string = "cssdost-theme") {
  if (typeof window !== "undefined") {
    localStorage.removeItem(storageKey);
    // Force reload to apply default theme
    window.location.reload();
  }
}

/**
 * Get the current stored theme preference
 */
export function getStoredTheme(storageKey: string = "cssdost-theme"): string | null {
  if (typeof window !== "undefined") {
    return localStorage.getItem(storageKey);
  }
  return null;
}

/**
 * Set a specific theme preference
 */
export function setStoredTheme(theme: "light" | "dark" | "system", storageKey: string = "cssdost-theme") {
  if (typeof window !== "undefined") {
    localStorage.setItem(storageKey, theme);
  }
}

/**
 * Check if the current theme is dark mode
 */
export function isDarkMode(): boolean {
  if (typeof window !== "undefined") {
    return document.documentElement.classList.contains("dark");
  }
  return false;
}

/**
 * Force light mode (useful for debugging)
 */
export function forceLightMode() {
  if (typeof window !== "undefined") {
    localStorage.removeItem("cssdost-theme");
    document.documentElement.classList.remove("dark");
    document.documentElement.classList.add("light");
  }
}

/**
 * Force dark mode (useful for debugging)
 */
export function forceDarkMode() {
  if (typeof window !== "undefined") {
    localStorage.setItem("cssdost-theme", "dark");
    document.documentElement.classList.remove("light");
    document.documentElement.classList.add("dark");
  }
}
