/**
 * Theme utility functions for CSSDOST
 */

/**
 * Clear the stored theme preference and reset to default
 */
export function clearStoredTheme(storageKey: string = "cssdost-theme") {
  if (typeof window !== "undefined") {
    localStorage.removeItem(storageKey);
    // Force reload to apply default theme
    window.location.reload();
  }
}

/**
 * Get the current stored theme preference
 */
export function getStoredTheme(storageKey: string = "cssdost-theme"): string | null {
  if (typeof window !== "undefined") {
    return localStorage.getItem(storageKey);
  }
  return null;
}

/**
 * Set a specific theme preference
 */
export function setStoredTheme(theme: "light" | "dark" | "system", storageKey: string = "cssdost-theme") {
  if (typeof window !== "undefined") {
    localStorage.setItem(storageKey, theme);
  }
}

/**
 * Check if the current theme is dark mode
 */
export function isDarkMode(): boolean {
  if (typeof window !== "undefined") {
    return document.documentElement.classList.contains("dark");
  }
  return false;
}

/**
 * Force light mode (useful for debugging)
 */
export function forceLightMode() {
  if (typeof window !== "undefined") {
    localStorage.removeItem("cssdost-theme");
    document.documentElement.classList.remove("dark");
    document.documentElement.classList.add("light");
  }
}

/**
 * Force dark mode (useful for debugging)
 */
export function forceDarkMode() {
  if (typeof window !== "undefined") {
    localStorage.setItem("cssdost-theme", "dark");
    document.documentElement.classList.remove("light");
    document.documentElement.classList.add("dark");
  }
}

/**
 * Initialize and ensure light mode as default theme
 * This function ensures light mode is set as default and clears any conflicting theme settings
 */
export function initializeLightModeDefault(storageKey: string = "cssdost-theme") {
  if (typeof window !== "undefined") {
    const storedTheme = localStorage.getItem(storageKey);

    // If no theme is stored, or if we want to reset to light mode default
    if (!storedTheme || !["light", "dark", "system"].includes(storedTheme)) {
      localStorage.setItem(storageKey, "light");
      document.documentElement.classList.remove("dark");
      document.documentElement.classList.add("light");
    }
  }
}

/**
 * Get the effective theme (resolves 'system' to actual theme)
 */
export function getEffectiveTheme(storageKey: string = "cssdost-theme"): "light" | "dark" {
  if (typeof window !== "undefined") {
    const storedTheme = localStorage.getItem(storageKey);

    if (storedTheme === "system") {
      return window.matchMedia("(prefers-color-scheme: dark)").matches ? "dark" : "light";
    }

    if (storedTheme === "dark") {
      return "dark";
    }

    // Default to light for any other case (including "light" and null/undefined)
    return "light";
  }

  // Server-side default
  return "light";
}
