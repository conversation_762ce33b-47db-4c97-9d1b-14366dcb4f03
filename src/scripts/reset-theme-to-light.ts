/**
 * <PERSON>ript to reset theme to light mode
 * This can be used to ensure light mode is the default theme
 */

import { initializeLightModeDefault, forceLightMode, clearStoredTheme } from "@/lib/theme-utils";

/**
 * Reset theme to light mode with various options
 */
export function resetToLightMode(options: {
  clearStorage?: boolean;
  forceApply?: boolean;
  storageKey?: string;
} = {}) {
  const {
    clearStorage = false,
    forceApply = true,
    storageKey = "cssdost-theme"
  } = options;

  console.log("🌞 Resetting theme to light mode...");

  if (clearStorage) {
    console.log("🗑️ Clearing stored theme preferences...");
    clearStoredTheme(storageKey);
  }

  if (forceApply) {
    console.log("✨ Forcing light mode application...");
    forceLightMode();
  } else {
    console.log("🔧 Initializing light mode as default...");
    initializeLightModeDefault(storageKey);
  }

  console.log("✅ Light mode has been set as default!");
}

/**
 * Check current theme status
 */
export function checkThemeStatus(storageKey: string = "cssdost-theme") {
  if (typeof window === "undefined") {
    console.log("🔍 Theme Status: Server-side (default: light)");
    return;
  }

  const storedTheme = localStorage.getItem(storageKey);
  const hasLightClass = document.documentElement.classList.contains("light");
  const hasDarkClass = document.documentElement.classList.contains("dark");
  const systemPrefersDark = window.matchMedia("(prefers-color-scheme: dark)").matches;

  console.log("🔍 Theme Status Report:");
  console.log(`   📦 Stored theme: ${storedTheme || "none"}`);
  console.log(`   🎨 HTML classes: light=${hasLightClass}, dark=${hasDarkClass}`);
  console.log(`   🖥️ System preference: ${systemPrefersDark ? "dark" : "light"}`);
  
  let effectiveTheme = "light";
  if (storedTheme === "dark" || (storedTheme === "system" && systemPrefersDark)) {
    effectiveTheme = "dark";
  }
  
  console.log(`   ✨ Effective theme: ${effectiveTheme}`);
}

// For browser console usage
if (typeof window !== "undefined") {
  (window as any).resetToLightMode = resetToLightMode;
  (window as any).checkThemeStatus = checkThemeStatus;
  
  console.log("🌞 Theme utilities loaded! Available commands:");
  console.log("   resetToLightMode() - Reset to light mode");
  console.log("   checkThemeStatus() - Check current theme status");
}
