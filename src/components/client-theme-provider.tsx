"use client";

import { useEffect, useState } from "react";
import { ThemeProvider } from "./theme-provider";

interface ClientThemeProviderProps {
  children: React.ReactNode;
  defaultTheme?: "light" | "dark" | "system";
  storageKey?: string;
}

export function ClientThemeProvider({
  children,
  defaultTheme = "light",
  storageKey = "cssdost-theme",
}: ClientThemeProviderProps) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    // Return a simple div with the default theme class during SSR
    return (
      <div className="light">
        {children}
      </div>
    );
  }

  return (
    <ThemeProvider defaultTheme={defaultTheme} storageKey={storageKey}>
      {children}
    </ThemeProvider>
  );
}
